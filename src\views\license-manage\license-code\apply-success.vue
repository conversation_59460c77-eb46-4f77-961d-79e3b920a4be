<template>
  <div class="app-container">
    <div class="success-page-modern">
      <!-- 成功状态展示 -->
      <div class="success-header-modern">
        <div class="success-animation">
          <div class="success-icon-modern">
            <el-icon size="72" color="#ffffff">
              <svg viewBox="0 0 1024 1024">
                <path fill="currentColor" d="M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896zm-55.808 536.384-99.52-99.584a38.4 38.4 0 1 0-54.336 54.336l126.72 126.72a38.272 38.272 0 0 0 54.336 0l262.4-262.464a38.4 38.4 0 1 0-54.272-54.336L456.192 600.384z"/>
              </svg>
            </el-icon>
          </div>
          <div class="success-ripple"></div>
          <div class="success-ripple success-ripple-delay"></div>
        </div>
        <h1 class="success-title">申请成功！</h1>
        <p class="success-subtitle">您的授权码已成功生成，请查看详细信息并下载说明书</p>
        <div class="success-badge">
          <el-tag type="success" size="large" effect="light">
            <el-icon class="mr-1"><Check /></el-icon>
            已生成
          </el-tag>
        </div>
      </div>

      <!-- 主要内容区域 - 网格布局 -->
      <div class="content-grid">
        <!-- 基本信息卡片 -->
        <el-card class="info-card-modern basic-card" shadow="hover">
          <template #header>
            <div class="card-header-modern">
              <div class="card-icon">
                <el-icon size="20" color="var(--el-color-primary)">
                  <Document />
                </el-icon>
              </div>
              <h3 class="card-title-modern">基本信息</h3>
            </div>
          </template>
          <div class="card-content">
            <div class="info-item">
              <span class="info-label-modern">销售订单编号</span>
              <el-text class="info-value-modern" type="primary" tag="strong">{{ licenseInfo?.salesOrderNo }}</el-text>
            </div>
            <div class="info-item">
              <span class="info-label-modern">项目销售</span>
              <el-text class="info-value-modern">{{ licenseInfo?.sellerName }}</el-text>
            </div>
            <div class="info-item full-width">
              <span class="info-label-modern">销售项目名称</span>
              <el-text class="info-value-modern">{{ licenseInfo?.salesProjectName }}</el-text>
            </div>
            <div class="info-item full-width">
              <span class="info-label-modern">最终客户名称</span>
              <el-text class="info-value-modern">{{ licenseInfo?.finalCustomerName }}</el-text>
            </div>
          </div>
        </el-card>

        <!-- 授权信息卡片 -->
        <el-card class="info-card-modern license-card" shadow="hover">
          <template #header>
            <div class="card-header-modern">
              <div class="card-icon">
                <el-icon size="20" color="var(--el-color-success)">
                  <Key />
                </el-icon>
              </div>
              <h3 class="card-title-modern">授权信息</h3>
            </div>
          </template>
          <div class="card-content">
            <div class="info-item">
              <span class="info-label-modern">授权类型</span>
              <el-tag type="info" size="small" effect="light">{{ licenseInfo?.licenseType }}</el-tag>
            </div>
            <div class="info-item">
              <span class="info-label-modern">有效期</span>
              <el-text class="info-value-modern" type="warning">{{ licenseInfo?.validType }}</el-text>
            </div>
            <div class="info-item">
              <span class="info-label-modern">并发登录数量</span>
              <el-text class="info-value-modern" type="success" tag="strong">{{ licenseInfo?.terminalLicenseCount }}</el-text>
            </div>
            <div class="info-item">
              <span class="info-label-modern">维保到期时间</span>
              <el-text class="info-value-modern">{{ licenseInfo?.maintenanceValidTime }}</el-text>
            </div>
            <div class="info-item full-width">
              <span class="info-label-modern">授权产品信息</span>
              <el-text class="info-value-modern">{{ licenseInfo?.licenseProductInfo }}</el-text>
            </div>
          </div>
        </el-card>

        <!-- 授权码信息卡片 -->
        <el-card class="info-card-modern code-card" shadow="hover">
          <template #header>
            <div class="card-header-modern">
              <div class="card-icon">
                <el-icon size="20" color="var(--el-color-warning)">
                  <Lock />
                </el-icon>
              </div>
              <h3 class="card-title-modern">授权码信息</h3>
              <el-button
                size="small"
                type="primary"
                text
                @click="copyLicenseCode"
                :icon="DocumentCopy"
              >
                复制
              </el-button>
            </div>
          </template>
          <div class="card-content">
            <div class="license-code-container">
              <el-text class="license-code-text" tag="code">{{ licenseInfo?.licenseCodeInfo }}</el-text>
            </div>
          </div>
        </el-card>

        <!-- 申请信息卡片 -->
        <el-card class="info-card-modern apply-card" shadow="hover">
          <template #header>
            <div class="card-header-modern">
              <div class="card-icon">
                <el-icon size="20" color="var(--el-color-info)">
                  <User />
                </el-icon>
              </div>
              <h3 class="card-title-modern">申请信息</h3>
            </div>
          </template>
          <div class="card-content">
            <div class="info-item">
              <span class="info-label-modern">申请人</span>
              <el-text class="info-value-modern">{{ licenseInfo?.applicant }}</el-text>
            </div>
            <div class="info-item">
              <span class="info-label-modern">申请时间</span>
              <el-text class="info-value-modern">{{ licenseInfo?.applyDate }}</el-text>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 操作按钮区域 -->
      <div class="action-section">
        <div class="action-buttons">
          <el-button
            type="primary"
            size="large"
            :loading="downloadLoading"
            @click="downloadLicensePDF()"
            class="download-btn-modern"
          >
            <el-icon class="mr-2">
              <Download />
            </el-icon>
            下载授权说明书
          </el-button>
          <el-button
            size="large"
            @click="goBack()"
            class="back-btn-modern"
          >
            <el-icon class="mr-2">
              <ArrowLeft />
            </el-icon>
            返回
          </el-button>
        </div>
        <div class="action-tips">
          <el-alert
            title="温馨提示"
            type="info"
            effect="light"
            :closable="false"
            show-icon
          >
            <template #default>
              请妥善保管您的授权码信息，如有疑问请联系技术支持。
            </template>
          </el-alert>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import Base64 from "@/utils/base64";
  import { useRoute, useRouter } from "vue-router";
  import LicenseCodeAPI from "@/api/license-manage/license-code.api";
  import { downloadFile } from "@/utils";
  import { ElMessage } from "element-plus";
  import {
    Check,
    Document,
    Key,
    Lock,
    User,
    Download,
    ArrowLeft,
    DocumentCopy
  } from "@element-plus/icons-vue";

  const route = useRoute();
  const router = useRouter();
  const data = Base64.decode(route.query.info);
  const licenseInfo = ref();

  onMounted(() => {
    LicenseCodeAPI.detail(data.id).then((response) => {
      licenseInfo.value = response;
    });
  });

  // 下载文件
  const downloadLoading = ref(false);
  const downloadLicensePDF = () => {
    downloadLoading.value = true;
    LicenseCodeAPI.downloadLicenseFile(data.id).then((response) => {
      downloadFile(response.data, "授权使用说明书.pdf");
      downloadLoading.value = false;
    });
  };

  // 复制授权码
  const copyLicenseCode = async () => {
    try {
      await navigator.clipboard.writeText(licenseInfo.value?.licenseCodeInfo || '');
      ElMessage.success('授权码已复制到剪贴板');
    } catch (err) {
      // 降级方案
      const textArea = document.createElement('textarea');
      textArea.value = licenseInfo.value?.licenseCodeInfo || '';
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      ElMessage.success('授权码已复制到剪贴板');
    }
  };

  // 返回
  const goBack = () => {
    router.back();
  };
</script>

<style scoped>
/* 现代化成功页面样式 */
.success-page-modern {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  min-height: calc(100vh - 120px);
}

/* 成功状态展示区域 */
.success-header-modern {
  text-align: center;
  margin-bottom: 48px;
  padding: 40px 20px;
  background: linear-gradient(135deg, var(--el-color-success-light-9) 0%, var(--el-color-success-light-8) 100%);
  border-radius: 20px;
  position: relative;
  overflow: hidden;
}

.success-header-modern::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  animation: rotate 20s linear infinite;
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.success-animation {
  position: relative;
  display: inline-block;
  margin-bottom: 24px;
}

.success-icon-modern {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--el-color-success) 0%, var(--el-color-success-light-3) 100%);
  box-shadow: 0 8px 32px rgba(35, 195, 67, 0.3);
  animation: successBounce 0.8s ease-out;
  position: relative;
  z-index: 2;
}

@keyframes successBounce {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.success-ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100px;
  height: 100px;
  border: 2px solid var(--el-color-success);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: ripple 2s ease-out infinite;
  opacity: 0;
}

.success-ripple-delay {
  animation-delay: 1s;
}

@keyframes ripple {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.8;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

.success-title {
  font-size: 32px;
  font-weight: 700;
  color: var(--el-text-color-primary);
  margin: 0 0 12px 0;
  animation: slideUp 0.6s ease-out 0.2s both;
}

.success-subtitle {
  font-size: 16px;
  color: var(--el-text-color-regular);
  margin: 0 0 24px 0;
  line-height: 1.6;
  animation: slideUp 0.6s ease-out 0.4s both;
}

.success-badge {
  animation: slideUp 0.6s ease-out 0.6s both;
}

@keyframes slideUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 内容网格布局 */
.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

/* 现代化信息卡片 */
.info-card-modern {
  border-radius: 16px;
  border: 1px solid var(--el-border-color-lighter);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  background: var(--el-bg-color);
}

.info-card-modern:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.1);
  border-color: var(--el-color-primary-light-7);
}

.card-header-modern {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 20px 24px;
  background: var(--el-fill-color-extra-light);
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background: var(--el-color-primary-light-9);
}

.card-title-modern {
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0;
  flex: 1;
}

.card-content {
  padding: 24px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  padding: 12px 0;
  border-bottom: 1px solid var(--el-border-color-extra-light);
}

.info-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
}

.info-item.full-width {
  flex-direction: column;
  gap: 8px;
}

.info-label-modern {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-regular);
  min-width: 120px;
  flex-shrink: 0;
}

.info-value-modern {
  font-size: 14px;
  font-weight: 500;
  text-align: right;
  word-break: break-all;
}

.full-width .info-value-modern {
  text-align: left;
}

/* 授权码特殊样式 */
.code-card {
  grid-column: 1 / -1;
}

.license-code-container {
  background: var(--el-fill-color-light);
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  padding: 16px;
  position: relative;
}

.license-code-text {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.6;
  color: var(--el-text-color-primary);
  word-break: break-all;
  display: block;
  background: transparent;
  border: none;
  outline: none;
  resize: none;
  width: 100%;
}

/* 操作区域 */
.action-section {
  text-align: center;
  padding: 32px 0;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 24px;
}

.download-btn-modern {
  min-width: 180px;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  background: linear-gradient(135deg, var(--el-color-primary) 0%, var(--el-color-primary-light-3) 100%);
  border: none;
  box-shadow: 0 4px 16px rgba(64, 128, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.download-btn-modern:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(64, 128, 255, 0.4);
}

.download-btn-modern:active {
  transform: translateY(0);
}

.back-btn-modern {
  min-width: 120px;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 12px;
  border: 2px solid var(--el-border-color-light);
  background: var(--el-bg-color);
  color: var(--el-text-color-regular);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.back-btn-modern:hover {
  border-color: var(--el-color-primary);
  color: var(--el-color-primary);
  transform: translateY(-1px);
}

.action-tips {
  max-width: 500px;
  margin: 0 auto;
}

/* 特殊卡片样式 */
.basic-card .card-icon {
  background: var(--el-color-primary-light-9);
}

.license-card .card-icon {
  background: var(--el-color-success-light-9);
}

.code-card .card-icon {
  background: var(--el-color-warning-light-9);
}

.apply-card .card-icon {
  background: var(--el-color-info-light-9);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .content-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .success-page-modern {
    padding: 16px;
  }

  .success-header-modern {
    padding: 32px 16px;
    margin-bottom: 32px;
  }

  .success-title {
    font-size: 28px;
  }

  .success-subtitle {
    font-size: 15px;
  }

  .success-icon-modern {
    width: 80px;
    height: 80px;
  }

  .success-icon-modern .el-icon {
    font-size: 56px !important;
  }

  .content-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .card-header-modern {
    padding: 16px 20px;
  }

  .card-content {
    padding: 20px;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }

  .info-label-modern {
    min-width: auto;
    font-size: 13px;
  }

  .info-value-modern {
    text-align: left;
    font-size: 14px;
  }

  .action-buttons {
    flex-direction: column;
    align-items: center;
    gap: 12px;
  }

  .download-btn-modern,
  .back-btn-modern {
    width: 100%;
    max-width: 280px;
  }
}

@media (max-width: 480px) {
  .success-page-modern {
    padding: 12px;
  }

  .success-header-modern {
    padding: 24px 12px;
    margin-bottom: 24px;
  }

  .success-title {
    font-size: 24px;
  }

  .success-subtitle {
    font-size: 14px;
  }

  .success-icon-modern {
    width: 70px;
    height: 70px;
  }

  .success-icon-modern .el-icon {
    font-size: 48px !important;
  }

  .card-header-modern {
    padding: 12px 16px;
  }

  .card-content {
    padding: 16px;
  }

  .card-title-modern {
    font-size: 16px;
  }

  .info-item {
    padding: 8px 0;
    margin-bottom: 12px;
  }

  .license-code-container {
    padding: 12px;
  }

  .license-code-text {
    font-size: 12px;
  }
}

/* 动画增强 */
.info-card-modern {
  animation: cardFadeIn 0.6s ease-out both;
}

.info-card-modern:nth-child(1) { animation-delay: 0.1s; }
.info-card-modern:nth-child(2) { animation-delay: 0.2s; }
.info-card-modern:nth-child(3) { animation-delay: 0.3s; }
.info-card-modern:nth-child(4) { animation-delay: 0.4s; }

@keyframes cardFadeIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.action-section {
  animation: slideUp 0.6s ease-out 0.8s both;
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .success-header-modern {
    background: linear-gradient(135deg, rgba(35, 195, 67, 0.1) 0%, rgba(35, 195, 67, 0.05) 100%);
  }

  .info-card-modern {
    background: var(--el-bg-color-overlay);
  }

  .license-code-container {
    background: var(--el-fill-color-darker);
    border-color: var(--el-border-color);
  }
}
</style>
