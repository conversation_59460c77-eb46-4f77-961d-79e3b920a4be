<template>
  <div class="app-container">
    <el-container>
      <el-header class="query-container">
        <el-form :model="queryForm" ref="queryFormRef" label-width="100px">
          <el-row type="flex" :gutter="50" justify="center">
            <el-col :span="6">
              <el-form-item label="销售订单编号" prop="salesOrderNo">
                <el-input
                  placeholder="请输入销售订单编号"
                  v-model="queryForm.salesOrderNo"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="销售项目名称" prop="salesProjectName">
                <el-input
                  placeholder="请输入销售项目名称"
                  v-model="queryForm.salesProjectName"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="最终客户名称" prop="finalCustomerName">
                <el-input
                  placeholder="请输入最终客户名称"
                  v-model="queryForm.finalCustomerName"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex" :gutter="50" justify="center">
            <el-col :span="6">
              <el-form-item label="项目销售" prop="sellerName">
                <el-input
                  placeholder="请输入项目销售"
                  v-model="queryForm.sellerName"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="授权类型" prop="licenseType">
                <el-select placeholder="请选择授权类型" v-model="queryForm.licenseType" clearable>
                  <el-option v-for="it in LICENSE_TYPE_OPTIONS" :label="it" :value="it"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="授权码信息" prop="licenseCodeInfo">
                <el-input
                  placeholder="请输入授权码信息"
                  v-model="queryForm.licenseCodeInfo"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex" justify="center">
            <el-col :span="6">
              <el-form-item>
                <el-button type="primary" @click="pageQuery()" :icon="Search">查询</el-button>
                <el-button @click="resetQuery()" :icon="Aim">重置</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-header>
      <el-main class="table-container">
        <el-button type="primary" class="apply-btn" @click="applyForLicenseCode()" :icon="DocumentAdd">
          授权码申请
        </el-button>
        <VxePageTable ref="tableRef" height="380" :fetch="fetchTableData" :queryParam="queryForm">
          <vxe-column type="seq" width="70"></vxe-column>
          <vxe-column field="salesOrderNo" title="销售订单编号" min-width="120"></vxe-column>
          <vxe-column field="salesProjectName" title="销售项目名称" min-width="120"></vxe-column>
          <vxe-column field="salesProjectName" title="最终客户名称" min-width="120"></vxe-column>
          <vxe-column field="sellerName" title="项目销售" min-width="120"></vxe-column>
          <vxe-column field="licenseProductInfo" title="授权产品信息" min-width="220"></vxe-column>
          <vxe-column field="licenseCodeInfo" title="授权码信息" min-width="320"></vxe-column>
          <vxe-column
            field="terminalLicenseCount"
            title="终端许可数量"
            min-width="220"
          ></vxe-column>
          <vxe-column field="validType" title="有效期" min-width="120"></vxe-column>
          <vxe-column field="maintenanceValidTime" title="维保到期时间" min-width="160"></vxe-column>
          <vxe-column field="licenseType" title="授权类型" min-width="120"></vxe-column>
          <vxe-column field="documentation" title="证明文件" min-width="120">
            <template #default="{ row }">
              <el-link type="primary" :underline="true" @click="downloadDocumentation(row)">
                {{ row.documentation }}
              </el-link>
            </template>
          </vxe-column>
          <vxe-column field="authStatus" title="授权状态" min-width="120">
            <template #default="{ row }">
              <el-tag v-if="row.authStatus == '已使用'" type="success">{{ row.authStatus }}</el-tag>
              <el-tag v-else type="warning">{{ row.authStatus }}</el-tag>
            </template>
          </vxe-column>
          <vxe-column field="applicant" title="申请人" min-width="120"></vxe-column>
          <vxe-column field="applyDate" title="申请时间" min-width="180"></vxe-column>
          <vxe-column title="操作" fixed="right" min-width="120">
            <template #default="{ row }">
              <el-button
                type="text"
                size="small"
                @click="downloadLicensePDF(row)"
                :loading="row.downloadLoading"
                :icon="Download"
              >
                下载授权
              </el-button>
            </template>
          </vxe-column>
        </VxePageTable>
      </el-main>
    </el-container>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from "vue";
  import { LicenseCodePageQuery } from "@/api/license-manage/types/license-code.type";
  import LicenseCodeAPI from "@/api/license-manage/license-code.api";
  import { downloadFile } from "@/utils";
  import { useRouter } from "vue-router";
  import { LICENSE_TYPE_OPTIONS } from "@/constants";
  import { Search, Aim, DocumentAdd, Download } from "@element-plus/icons-vue";

  const router = useRouter();

  const queryFormRef = ref();

  let queryForm = ref({
    salesOrderNo: "",
    salesProjectName: "",
    finalCustomerName: "",
    sellerName: "",
    licenseType: "",
    licenseCodeInfo: "",
  });

  const tableRef = ref();

  // 拉取数据
  function fetchTableData(params: LicenseCodePageQuery) {
    return LicenseCodeAPI.listPageQuery(params);
  }

  // 查询
  function pageQuery() {
    tableRef.value.refresh();
  }

  // 重置查询
  function resetQuery() {
    queryFormRef.value.resetFields();
    pageQuery();
  }

  /** 下载证明文件 */
  function downloadDocumentation({
    documentation,
    documentationUrl,
  }: {
    documentation: string;
    documentationUrl: string;
  }) {
    LicenseCodeAPI.downloadDocumentation(documentationUrl).then((response) => {
      downloadFile(response.data, documentation);
    });
  }

  // 下载授权
  function downloadLicensePDF(row: any) {
    row.downloadLoading = true;
    LicenseCodeAPI.downloadLicenseFile(row.id).then((response) => {
      downloadFile(response.data, "授权使用说明书.pdf");
      row.downloadLoading = false;
    });
  }

  // 授权码申请
  function applyForLicenseCode() {
    router.push("/license-code/apply");
  }
</script>

<style scoped>
  .query-container {
    height: 200px;
    margin-bottom: 20px;
    padding: 50px;
    background-color: #fff;
  }
  .table-container {
    background-color: #fff;
  }
  .table-container > .apply-btn {
    margin-bottom: 10px;
  }
</style>
