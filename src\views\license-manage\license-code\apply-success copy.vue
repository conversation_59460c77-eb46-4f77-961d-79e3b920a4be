<template>
  <div class="app-container">
    <div class="success-page-compact">
      <!-- 成功状态展示 - 紧凑版 -->
      <div class="success-header-compact">
        <div class="success-content-wrapper">
          <div class="success-icon-compact">
            <el-icon size="48" color="#ffffff">
              <svg viewBox="0 0 1024 1024">
                <path fill="currentColor" d="M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896zm-55.808 536.384-99.52-99.584a38.4 38.4 0 1 0-54.336 54.336l126.72 126.72a38.272 38.272 0 0 0 54.336 0l262.4-262.464a38.4 38.4 0 1 0-54.272-54.336L456.192 600.384z"/>
              </svg>
            </el-icon>
          </div>
          <div class="success-text">
            <h1 class="success-title-compact">申请成功！</h1>
            <p class="success-subtitle-compact">您的授权码已成功生成</p>
          </div>
          <div class="success-badge-compact">
            <el-tag type="success" size="default" effect="light">
              <el-icon class="mr-1"><Check /></el-icon>
              已生成
            </el-tag>
          </div>
        </div>
      </div>

      <!-- 主要内容区域 - 单卡片布局 -->
      <el-card class="main-info-card" shadow="hover">
        <template #header>
          <div class="main-card-header">
            <h2 class="main-card-title">授权码详细信息</h2>
            <div class="header-actions">
              <el-button
                size="small"
                type="primary"
                text
                @click="copyLicenseCode"
                :icon="DocumentCopy"
              >
                复制授权码
              </el-button>
            </div>
          </div>
        </template>

        <div class="main-card-content">
          <!-- 授权码信息 - 突出显示 -->
          <div class="license-code-section">
            <div class="section-header">
              <el-icon size="18" color="var(--el-color-warning)">
                <Lock />
              </el-icon>
              <span class="section-title">授权码信息</span>
            </div>
            <div class="license-code-display">
              <el-text class="license-code-text" tag="code">{{ licenseInfo?.licenseCodeInfo }}</el-text>
            </div>
          </div>

          <!-- 信息网格 -->
          <div class="info-grid">
            <!-- 基本信息 -->
            <div class="info-section">
              <div class="section-header">
                <el-icon size="18" color="var(--el-color-primary)">
                  <Document />
                </el-icon>
                <span class="section-title">基本信息</span>
              </div>
              <div class="info-rows">
                <div class="info-row">
                  <span class="info-label">销售订单编号</span>
                  <el-text class="info-value" type="primary" tag="strong">{{ licenseInfo?.salesOrderNo }}</el-text>
                </div>
                <div class="info-row">
                  <span class="info-label">项目销售</span>
                  <el-text class="info-value">{{ licenseInfo?.sellerName }}</el-text>
                </div>
                <div class="info-row">
                  <span class="info-label">销售项目名称</span>
                  <el-text class="info-value">{{ licenseInfo?.salesProjectName }}</el-text>
                </div>
                <div class="info-row">
                  <span class="info-label">最终客户名称</span>
                  <el-text class="info-value">{{ licenseInfo?.finalCustomerName }}</el-text>
                </div>
              </div>
            </div>

            <!-- 授权信息 -->
            <div class="info-section">
              <div class="section-header">
                <el-icon size="18" color="var(--el-color-success)">
                  <Key />
                </el-icon>
                <span class="section-title">授权信息</span>
              </div>
              <div class="info-rows">
                <div class="info-row">
                  <span class="info-label">授权类型</span>
                  <el-tag type="info" size="small" effect="light">{{ licenseInfo?.licenseType }}</el-tag>
                </div>
                <div class="info-row">
                  <span class="info-label">有效期</span>
                  <el-text class="info-value" type="warning">{{ licenseInfo?.validType }}</el-text>
                </div>
                <div class="info-row">
                  <span class="info-label">并发登录数量</span>
                  <el-text class="info-value" type="success" tag="strong">{{ licenseInfo?.terminalLicenseCount }}</el-text>
                </div>
                <div class="info-row">
                  <span class="info-label">维保到期时间</span>
                  <el-text class="info-value">{{ licenseInfo?.maintenanceValidTime }}</el-text>
                </div>
                <div class="info-row">
                  <span class="info-label">授权产品信息</span>
                  <el-text class="info-value">{{ licenseInfo?.licenseProductInfo }}</el-text>
                </div>
              </div>
            </div>

            <!-- 申请信息 -->
            <div class="info-section">
              <div class="section-header">
                <el-icon size="18" color="var(--el-color-info)">
                  <User />
                </el-icon>
                <span class="section-title">申请信息</span>
              </div>
              <div class="info-rows">
                <div class="info-row">
                  <span class="info-label">申请人</span>
                  <el-text class="info-value">{{ licenseInfo?.applicant }}</el-text>
                </div>
                <div class="info-row">
                  <span class="info-label">申请时间</span>
                  <el-text class="info-value">{{ licenseInfo?.applyDate }}</el-text>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 操作按钮区域 - 紧凑版 -->
      <div class="action-section-compact">
        <div class="action-buttons-compact">
          <el-button
            type="primary"
            size="large"
            :loading="downloadLoading"
            @click="downloadLicensePDF()"
            class="download-btn-compact"
          >
            <el-icon class="mr-2">
              <Download />
            </el-icon>
            下载授权说明书
          </el-button>
          <el-button
            size="large"
            @click="goBack()"
            class="back-btn-compact"
          >
            <el-icon class="mr-2">
              <ArrowLeft />
            </el-icon>
            返回
          </el-button>
        </div>
        <div class="action-tips-compact">
          <el-alert
            title="温馨提示：请妥善保管您的授权码信息，如有疑问请联系技术支持。"
            type="info"
            effect="light"
            :closable="false"
            show-icon
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import Base64 from "@/utils/base64";
  import { useRoute, useRouter } from "vue-router";
  import LicenseCodeAPI from "@/api/license-manage/license-code.api";
  import { downloadFile } from "@/utils";
  import { ElMessage } from "element-plus";
  import {
    Check,
    Document,
    Key,
    Lock,
    User,
    Download,
    ArrowLeft,
    DocumentCopy
  } from "@element-plus/icons-vue";

  const route = useRoute();
  const router = useRouter();
  const data = Base64.decode(route.query.info);
  const licenseInfo = ref();

  onMounted(() => {
    LicenseCodeAPI.detail(data.id).then((response) => {
      licenseInfo.value = response;
    });
  });

  // 下载文件
  const downloadLoading = ref(false);
  const downloadLicensePDF = () => {
    downloadLoading.value = true;
    LicenseCodeAPI.downloadLicenseFile(data.id).then((response) => {
      downloadFile(response.data, "授权使用说明书.pdf");
      downloadLoading.value = false;
    });
  };

  // 复制授权码
  const copyLicenseCode = async () => {
    try {
      await navigator.clipboard.writeText(licenseInfo.value?.licenseCodeInfo || '');
      ElMessage.success('授权码已复制到剪贴板');
    } catch (err) {
      // 降级方案
      const textArea = document.createElement('textarea');
      textArea.value = licenseInfo.value?.licenseCodeInfo || '';
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      ElMessage.success('授权码已复制到剪贴板');
    }
  };

  // 返回
  const goBack = () => {
    router.back();
  };
</script>

<style scoped>
/* 紧凑版成功页面样式 */
.success-page-compact {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  min-height: calc(100vh - 100px);
}

/* 紧凑版成功状态展示区域 */
.success-header-compact {
  margin-bottom: 24px;
  padding: 20px 24px;
  background: linear-gradient(135deg, var(--el-color-success-light-9) 0%, var(--el-color-success-light-8) 100%);
  border-radius: 12px;
  border: 1px solid var(--el-color-success-light-7);
}

.success-content-wrapper {
  display: flex;
  align-items: center;
  gap: 16px;
}

.success-icon-compact {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--el-color-success) 0%, var(--el-color-success-light-3) 100%);
  box-shadow: 0 4px 16px rgba(35, 195, 67, 0.3);
  flex-shrink: 0;
}

.success-text {
  flex: 1;
}

.success-title-compact {
  font-size: 24px;
  font-weight: 700;
  color: var(--el-text-color-primary);
  margin: 0 0 4px 0;
}

.success-subtitle-compact {
  font-size: 14px;
  color: var(--el-text-color-regular);
  margin: 0;
  line-height: 1.5;
}

.success-badge-compact {
  flex-shrink: 0;
}

/* 主要信息卡片 */
.main-info-card {
  border-radius: 12px;
  border: 1px solid var(--el-border-color-lighter);
  margin-bottom: 20px;
  overflow: hidden;
}

.main-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  background: var(--el-fill-color-extra-light);
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.main-card-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.main-card-content {
  padding: 24px;
}

/* 授权码信息区域 */
.license-code-section {
  margin-bottom: 32px;
  padding: 20px;
  background: var(--el-fill-color-extra-light);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.license-code-display {
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: 6px;
  padding: 16px;
}

.license-code-text {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.6;
  color: var(--el-text-color-primary);
  word-break: break-all;
  display: block;
  background: transparent;
  border: none;
  outline: none;
  width: 100%;
}

/* 信息网格布局 */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.info-section {
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
  padding: 20px;
}

.info-rows {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 8px 0;
  border-bottom: 1px solid var(--el-border-color-extra-light);
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-regular);
  min-width: 120px;
  flex-shrink: 0;
}

.info-value {
  font-size: 14px;
  font-weight: 500;
  text-align: right;
  word-break: break-all;
  flex: 1;
  margin-left: 16px;
}

/* 操作区域 - 紧凑版 */
.action-section-compact {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 24px;
  padding: 20px 0;
}

.action-buttons-compact {
  display: flex;
  gap: 12px;
}

.download-btn-compact {
  min-width: 160px;
  height: 44px;
  font-size: 15px;
  font-weight: 600;
  border-radius: 8px;
  background: linear-gradient(135deg, var(--el-color-primary) 0%, var(--el-color-primary-light-3) 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(64, 128, 255, 0.3);
  transition: all 0.3s ease;
}

.download-btn-compact:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 128, 255, 0.4);
}

.back-btn-compact {
  min-width: 100px;
  height: 44px;
  font-size: 15px;
  font-weight: 500;
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
  background: var(--el-bg-color);
  color: var(--el-text-color-regular);
  transition: all 0.3s ease;
}

.back-btn-compact:hover {
  border-color: var(--el-color-primary);
  color: var(--el-color-primary);
}

.action-tips-compact {
  flex: 1;
  max-width: 500px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .info-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .success-page-compact {
    padding: 16px;
  }

  .success-header-compact {
    padding: 16px 20px;
    margin-bottom: 20px;
  }

  .success-content-wrapper {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .success-icon-compact {
    width: 56px;
    height: 56px;
  }

  .success-icon-compact .el-icon {
    font-size: 40px !important;
  }

  .success-title-compact {
    font-size: 20px;
  }

  .success-subtitle-compact {
    font-size: 13px;
  }

  .main-card-header {
    padding: 16px 20px;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .main-card-title {
    font-size: 18px;
  }

  .main-card-content {
    padding: 20px;
  }

  .license-code-section {
    padding: 16px;
    margin-bottom: 24px;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .info-section {
    padding: 16px;
  }

  .info-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .info-label {
    min-width: auto;
    font-size: 13px;
  }

  .info-value {
    text-align: left;
    font-size: 14px;
    margin-left: 0;
  }

  .action-section-compact {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .action-buttons-compact {
    justify-content: center;
    gap: 12px;
  }

  .download-btn-compact,
  .back-btn-compact {
    width: 100%;
    max-width: 200px;
  }

  .action-tips-compact {
    max-width: none;
  }
}

@media (max-width: 480px) {
  .success-page-compact {
    padding: 12px;
  }

  .success-header-compact {
    padding: 12px 16px;
    margin-bottom: 16px;
  }

  .success-icon-compact {
    width: 48px;
    height: 48px;
  }

  .success-icon-compact .el-icon {
    font-size: 32px !important;
  }

  .success-title-compact {
    font-size: 18px;
  }

  .success-subtitle-compact {
    font-size: 12px;
  }

  .main-card-header {
    padding: 12px 16px;
  }

  .main-card-title {
    font-size: 16px;
  }

  .main-card-content {
    padding: 16px;
  }

  .license-code-section {
    padding: 12px;
    margin-bottom: 20px;
  }

  .section-title {
    font-size: 14px;
  }

  .license-code-display {
    padding: 12px;
  }

  .license-code-text {
    font-size: 12px;
  }

  .info-section {
    padding: 12px;
  }

  .info-row {
    padding: 6px 0;
  }

  .action-buttons-compact {
    flex-direction: column;
    align-items: center;
  }

  .download-btn-compact,
  .back-btn-compact {
    width: 100%;
    max-width: 280px;
  }
}

/* 动画效果 */
.success-page-compact {
  animation: pageSlideIn 0.5s ease-out;
}

@keyframes pageSlideIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.main-info-card {
  animation: cardSlideIn 0.6s ease-out 0.1s both;
}

@keyframes cardSlideIn {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.info-section {
  animation: sectionFadeIn 0.5s ease-out both;
}

.info-section:nth-child(1) { animation-delay: 0.2s; }
.info-section:nth-child(2) { animation-delay: 0.3s; }
.info-section:nth-child(3) { animation-delay: 0.4s; }

@keyframes sectionFadeIn {
  0% {
    opacity: 0;
    transform: translateY(15px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.action-section-compact {
  animation: actionSlideIn 0.5s ease-out 0.5s both;
}

@keyframes actionSlideIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .success-header-compact {
    background: linear-gradient(135deg, rgba(35, 195, 67, 0.15) 0%, rgba(35, 195, 67, 0.08) 100%);
    border-color: rgba(35, 195, 67, 0.3);
  }

  .license-code-section {
    background: var(--el-fill-color-darker);
    border-color: var(--el-border-color);
  }

  .license-code-display {
    background: var(--el-fill-color-dark);
    border-color: var(--el-border-color);
  }

  .info-section {
    background: var(--el-bg-color-overlay);
    border-color: var(--el-border-color);
  }
}

/* 打印样式 */
@media print {
  .success-page-compact {
    padding: 0;
    max-width: none;
  }

  .action-section-compact {
    display: none;
  }

  .success-header-compact {
    background: none !important;
    border: 1px solid #ccc;
  }

  .main-info-card {
    box-shadow: none;
    border: 1px solid #ccc;
  }
}

/* 操作区域 */
.action-section {
  text-align: center;
  padding: 32px 0;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 24px;
}

.download-btn-modern {
  min-width: 180px;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  background: linear-gradient(135deg, var(--el-color-primary) 0%, var(--el-color-primary-light-3) 100%);
  border: none;
  box-shadow: 0 4px 16px rgba(64, 128, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.download-btn-modern:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(64, 128, 255, 0.4);
}

.download-btn-modern:active {
  transform: translateY(0);
}

.back-btn-modern {
  min-width: 120px;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 12px;
  border: 2px solid var(--el-border-color-light);
  background: var(--el-bg-color);
  color: var(--el-text-color-regular);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.back-btn-modern:hover {
  border-color: var(--el-color-primary);
  color: var(--el-color-primary);
  transform: translateY(-1px);
}

.action-tips {
  max-width: 500px;
  margin: 0 auto;
}

/* 特殊卡片样式 */
.basic-card .card-icon {
  background: var(--el-color-primary-light-9);
}

.license-card .card-icon {
  background: var(--el-color-success-light-9);
}

.code-card .card-icon {
  background: var(--el-color-warning-light-9);
}

.apply-card .card-icon {
  background: var(--el-color-info-light-9);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .content-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .success-page-modern {
    padding: 16px;
  }

  .success-header-modern {
    padding: 32px 16px;
    margin-bottom: 32px;
  }

  .success-title {
    font-size: 28px;
  }

  .success-subtitle {
    font-size: 15px;
  }

  .success-icon-modern {
    width: 80px;
    height: 80px;
  }

  .success-icon-modern .el-icon {
    font-size: 56px !important;
  }

  .content-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .card-header-modern {
    padding: 16px 20px;
  }

  .card-content {
    padding: 20px;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }

  .info-label-modern {
    min-width: auto;
    font-size: 13px;
  }

  .info-value-modern {
    text-align: left;
    font-size: 14px;
  }

  .action-buttons {
    flex-direction: column;
    align-items: center;
    gap: 12px;
  }

  .download-btn-modern,
  .back-btn-modern {
    width: 100%;
    max-width: 280px;
  }
}

@media (max-width: 480px) {
  .success-page-modern {
    padding: 12px;
  }

  .success-header-modern {
    padding: 24px 12px;
    margin-bottom: 24px;
  }

  .success-title {
    font-size: 24px;
  }

  .success-subtitle {
    font-size: 14px;
  }

  .success-icon-modern {
    width: 70px;
    height: 70px;
  }

  .success-icon-modern .el-icon {
    font-size: 48px !important;
  }

  .card-header-modern {
    padding: 12px 16px;
  }

  .card-content {
    padding: 16px;
  }

  .card-title-modern {
    font-size: 16px;
  }

  .info-item {
    padding: 8px 0;
    margin-bottom: 12px;
  }

  .license-code-container {
    padding: 12px;
  }

  .license-code-text {
    font-size: 12px;
  }
}

/* 动画增强 */
.info-card-modern {
  animation: cardFadeIn 0.6s ease-out both;
}

.info-card-modern:nth-child(1) { animation-delay: 0.1s; }
.info-card-modern:nth-child(2) { animation-delay: 0.2s; }
.info-card-modern:nth-child(3) { animation-delay: 0.3s; }
.info-card-modern:nth-child(4) { animation-delay: 0.4s; }

@keyframes cardFadeIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.action-section {
  animation: slideUp 0.6s ease-out 0.8s both;
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .success-header-modern {
    background: linear-gradient(135deg, rgba(35, 195, 67, 0.1) 0%, rgba(35, 195, 67, 0.05) 100%);
  }

  .info-card-modern {
    background: var(--el-bg-color-overlay);
  }

  .license-code-container {
    background: var(--el-fill-color-darker);
    border-color: var(--el-border-color);
  }
}
</style>
