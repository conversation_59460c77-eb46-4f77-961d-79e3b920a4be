import request from "@/utils/request";
import { ApplyForm, ApplySuccessInfo } from "./types/license-file.type";

const LICENSE_FILE_BASE_URL = "/license-file";

const LicenseFileAPI = {
  listPageQuery(params: any) {
    return request({
      url: `${LICENSE_FILE_BASE_URL}/listPageQuery`,
      method: "get",
      params,
    });
  },

  // 授权绑定表单提交申请
  apply(data: ApplyForm) {
    return request<any, ApplySuccessInfo>({
      url: `${LICENSE_FILE_BASE_URL}/apply`,
      method: "post",
      data,
    });
  },

  // 通过授权码获取机器码
  getMachineCode(licenseCode: string) {
    return request<any, string>({
      url: `${LICENSE_FILE_BASE_URL}/getMachineCode`,
      method: "get",
      params: { licenseCode },
    });
  },

  // 下载授权文件
  downloadLicenseFile(id: string) {
    return request({
      url: `${LICENSE_FILE_BASE_URL}/downloadLicenseFile`,
      method: "get",
      params: { id },
      responseType: "blob",
    });
  },
  // 注销码是否正确
  isCancelCodeCorrect(licenseCode: string, cancelCode: string) {
    return request<any, boolean>({
      url: `${LICENSE_FILE_BASE_URL}/isCancelCodeCorrect`,
      method: "get",
      params: { licenseCode, cancelCode },
    });
  },
};

export default LicenseFileAPI;
