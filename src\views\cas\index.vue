<template>
  <div class="login-container" v-show="showError">
    无系统登录权限，请联系相关负责人开通！
  </div>
</template>

<script setup lang="ts">
import { useRouter, useRoute } from "vue-router";
import { useUserStore } from "@/store";
const route = useRoute();
const router = useRouter();
const userStore = useUserStore();
const showError = ref(false);
onMounted(() => {
  const ticket = route.query.ticket as string;
  if (!ticket) {
    redirectToLogin();
    return;
  }
  handleLoginSubmit(ticket);
});

/**
 * 登录提交
 */
async function handleLoginSubmit(ticket: string) {
  try {
    // 1. 执行登录
    const res = await userStore.casLogin(ticket);
    if (!res) {
      redirectToLogin();
      return;
    }
    if (!res.accessToken) {
      showError.value = true;
      return;
    }

    // 2. 获取用户信息（包含用户角色，用于路由生成）
    await userStore.getUserInfo();

    // 通过替换当前路由触发路由守卫，让守卫处理后续的路由生成和跳转
    await router.replace("/");

  } catch (error) {
    // 6. 统一错误处理
    console.error("登录失败:", error);
    redirectToLogin();
  }
}


/**
 * 跳转去登录
 */
function redirectToLogin() {
  router.replace("/login");
}

</script>

<style lang="scss" scoped>
.login-container {
  padding: 10px 20px;
  font-weight: 500;
}
</style>
